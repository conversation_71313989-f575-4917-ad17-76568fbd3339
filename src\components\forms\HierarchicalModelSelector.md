# Hierarchical Model Selector Component

## Overview

The `HierarchicalModelSelector` component provides a visual hierarchical tree form for selecting model configurations in the Project Dockets creation page. It replaces the traditional dropdown approach with a more intuitive, step-by-step selection process that shows the dependency relationships between different model parameters.

## Features

### 🌳 Visual Hierarchy
- **4-Level Structure**: Model Type → Task Type → Framework → Architecture
- **Color-Coded Levels**: Each level has a distinct color scheme for easy identification
- **Progressive Disclosure**: Options are revealed step-by-step based on previous selections
- **Visual Indentation**: Each level is visually indented to show the hierarchy

### 🎯 Smart Dependencies
- **Dynamic Filtering**: Options are automatically filtered based on the dependency map
- **Cascading Reset**: Selecting a higher-level option resets all dependent lower levels
- **Real-time Validation**: Shows errors for each level independently

### 📊 Selection Path Display
- **Breadcrumb Trail**: Shows the current selection path with colored chips
- **Visual Feedback**: Icons and arrows guide the user through the selection process
- **Clear Progress**: Users can see their progress through the configuration

## Component Structure

```
HierarchicalModelSelector
├── Selection Path Display (Breadcrumb)
├── Level 1: Model Type (Blue theme)
├── Level 2: Task Type (Purple theme, indented)
├── Level 3: Framework (Green theme, more indented)
└── Level 4: Architecture (Orange theme, most indented)
```

## Data Dependencies

### Model Dependency Map
```typescript
const modelDependencyMap = {
  structured: {
    "tabular-classification": ["xgboost", "lightgbm", "scikit-learn", "tensorflow", "pytorch"],
    "tabular-regression": ["xgboost", "lightgbm", "scikit-learn", "tensorflow", "pytorch"]
  },
  unstructured: {
    "image-classification": ["pytorch", "onnx", "tensorflow"],
    "image-segmentation": ["pytorch", "onnx", "tensorflow"]
  }
};
```

### Framework to Architecture Map
```typescript
const frameworkToArchMap = {
  xgboost: ["tree-based", "neural-network", "custom"],
  lightgbm: ["tree-based", "neural-network", "custom"],
  tensorflow: ["tree-based", "neural-network", "custom"],
  pytorch: ["tree-based", "neural-network", "custom", "resnet-50", "efficientnet-b4", "vision-transformer"],
  onnx: ["resnet-50", "efficientnet-b4", "vision-transformer", "custom"]
};
```

## Usage Example

```tsx
<HierarchicalModelSelector
  modelType={hierarchicalModelType}
  taskType={hierarchicalTaskType}
  framework={hierarchicalFramework}
  architecture={hierarchicalArchitecture}
  onModelTypeChange={handleHierarchicalModelTypeChange}
  onTaskTypeChange={handleHierarchicalTaskTypeChange}
  onFrameworkChange={handleHierarchicalFrameworkChange}
  onArchitectureChange={handleHierarchicalArchitectureChange}
  modelDependencyMap={modelDependencyMap}
  frameworkToArchMap={frameworkToArchMap}
  errors={{
    modelType: "Model Type is required",
    taskType: undefined,
    framework: undefined,
    architecture: undefined,
  }}
/>
```

## Props Interface

```typescript
interface HierarchicalModelSelectorProps {
  modelType: string;
  taskType: string;
  framework: string;
  architecture: string;
  onModelTypeChange: (value: string) => void;
  onTaskTypeChange: (value: string) => void;
  onFrameworkChange: (value: string) => void;
  onArchitectureChange: (value: string) => void;
  modelDependencyMap: Record<string, Record<string, string[]>>;
  frameworkToArchMap: Record<string, string[]>;
  errors?: {
    modelType?: string;
    taskType?: string;
    framework?: string;
    architecture?: string;
  };
}
```

## Integration with Form

The component is integrated into the Project Dockets creation form and:

1. **Replaces Original Fields**: The original Model Type, Task Type, Model Framework, and Model Architecture dropdowns are filtered out from the regular form rendering
2. **Syncs with Formik**: All selections are automatically synchronized with the Formik form state
3. **Validation Integration**: Form validation errors are displayed at each level
4. **Form Submission**: Values are included in the form submission payload

## Benefits

### For Users
- **Intuitive Flow**: Step-by-step selection process is more user-friendly
- **Clear Dependencies**: Visual hierarchy makes relationships obvious
- **Reduced Errors**: Progressive disclosure prevents invalid combinations
- **Better Feedback**: Clear visual indicators for progress and errors

### For Developers
- **Maintainable**: Centralized dependency logic
- **Extensible**: Easy to add new model types or architectures
- **Type Safe**: Full TypeScript support
- **Reusable**: Component can be used in other forms

## Styling

The component uses Material-UI components with custom styling:
- **Color Themes**: Each level has a distinct color (Blue, Purple, Green, Orange)
- **Background Colors**: Subtle background colors indicate enabled/disabled states
- **Border Styling**: Colored borders match the level themes
- **Responsive Design**: Works on both desktop and mobile devices

## Future Enhancements

- **Search Functionality**: Add search within dropdown options
- **Tooltips**: Add explanatory tooltips for each option
- **Keyboard Navigation**: Enhanced keyboard accessibility
- **Animation**: Smooth transitions between states
- **Preset Configurations**: Quick selection of common configurations
