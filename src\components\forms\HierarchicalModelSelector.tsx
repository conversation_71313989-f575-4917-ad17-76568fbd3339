"use client";
import React from "react";
import {
  FormControl,
  Select,
  MenuItem,
  FormLabel,
  Box,
  Typography,
  Chip,
} from "@mui/material";
import { SelectChangeEvent } from "@mui/material/Select";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import AccountTreeIcon from "@mui/icons-material/AccountTree";

interface HierarchicalModelSelectorProps {
  modelType: string;
  taskType: string;
  framework: string;
  architecture: string;
  onModelTypeChange: (value: string) => void;
  onTaskTypeChange: (value: string) => void;
  onFrameworkChange: (value: string) => void;
  onArchitectureChange: (value: string) => void;
  modelDependencyMap: Record<string, Record<string, string[]>>;
  frameworkToArchMap: Record<string, string[]>;
  errors?: {
    modelType?: string;
    taskType?: string;
    framework?: string;
    architecture?: string;
  };
}

const HierarchicalModelSelector: React.FC<HierarchicalModelSelectorProps> = ({
  modelType,
  taskType,
  framework,
  architecture,
  onModelTypeChange,
  onTaskTypeChange,
  onFrameworkChange,
  onArchitectureChange,
  modelDependencyMap,
  frameworkToArchMap,
  errors = {},
}) => {
  const handleModelTypeChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    onModelTypeChange(value);
  };

  const handleTaskTypeChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    onTaskTypeChange(value);
  };

  const handleFrameworkChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    onFrameworkChange(value);
  };

  const handleArchitectureChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    onArchitectureChange(value);
  };

  const getTaskTypes = () => {
    if (!modelType) return [];
    const normalizedModelType = modelType.toLowerCase().trim();
    return Object.keys(modelDependencyMap[normalizedModelType] || {});
  };

  const getFrameworks = () => {
    if (!modelType || !taskType) return [];
    const normalizedModelType = modelType.toLowerCase().trim();
    const normalizedTaskType = taskType.toLowerCase().trim();
    return modelDependencyMap[normalizedModelType]?.[normalizedTaskType] || [];
  };

  const getArchitectures = () => {
    if (!framework) return [];
    const normalizedFramework = framework.toLowerCase().trim();
    return frameworkToArchMap[normalizedFramework] || [];
  };

  const renderHierarchyPath = () => {
    const pathItems = [];
    
    if (modelType) {
      pathItems.push(
        <Chip
          key="modelType"
          label={modelType}
          size="small"
          color="primary"
          variant="outlined"
        />
      );
    }
    
    if (taskType) {
      pathItems.push(<ArrowRightIcon key="arrow1" fontSize="small" />);
      pathItems.push(
        <Chip
          key="taskType"
          label={taskType}
          size="small"
          color="secondary"
          variant="outlined"
        />
      );
    }
    
    if (framework) {
      pathItems.push(<ArrowRightIcon key="arrow2" fontSize="small" />);
      pathItems.push(
        <Chip
          key="framework"
          label={framework}
          size="small"
          color="success"
          variant="outlined"
        />
      );
    }
    
    if (architecture) {
      pathItems.push(<ArrowRightIcon key="arrow3" fontSize="small" />);
      pathItems.push(
        <Chip
          key="architecture"
          label={architecture}
          size="small"
          color="warning"
          variant="outlined"
        />
      );
    }

    return pathItems.length > 0 ? (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1,
          mb: 2,
          p: 2,
          backgroundColor: "#f8f9fa",
          borderRadius: 1,
          border: "1px solid #e9ecef",
        }}
      >
        <AccountTreeIcon fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
          Selection Path:
        </Typography>
        {pathItems}
      </Box>
    ) : null;
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: "flex", alignItems: "center", gap: 1 }}>
        <AccountTreeIcon />
        Model Configuration Hierarchy
      </Typography>
      
      {renderHierarchyPath()}

      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Level 1: Model Type */}
        <Box sx={{ position: "relative" }}>
          <FormControl fullWidth error={!!errors.modelType}>
            <FormLabel sx={{ mb: 1, fontWeight: "bold", color: "#1976d2" }}>
              1. Model Type <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              value={modelType}
              onChange={handleModelTypeChange}
              displayEmpty
              sx={{
                backgroundColor: "#f3f4f6",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#1976d2",
                  borderWidth: 2,
                },
              }}
            >
              <MenuItem value="">
                <em>Select Model Type</em>
              </MenuItem>
              {Object.keys(modelDependencyMap).map((type) => (
                <MenuItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </MenuItem>
              ))}
            </Select>
            {errors.modelType && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.modelType}
              </Typography>
            )}
          </FormControl>
        </Box>

        {/* Level 2: Task Type */}
        <Box sx={{ position: "relative", ml: 2 }}>
          <FormControl fullWidth disabled={!modelType} error={!!errors.taskType}>
            <FormLabel sx={{ mb: 1, fontWeight: "bold", color: "#9c27b0" }}>
              2. Task Type <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              value={taskType}
              onChange={handleTaskTypeChange}
              displayEmpty
              sx={{
                backgroundColor: modelType ? "#f8f4ff" : "#f5f5f5",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#9c27b0",
                  borderWidth: 2,
                },
              }}
            >
              <MenuItem value="">
                <em>{modelType ? "Select Task Type" : "Select Model Type first"}</em>
              </MenuItem>
              {getTaskTypes().map((type) => (
                <MenuItem key={type} value={type}>
                  {type.split("-").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                </MenuItem>
              ))}
            </Select>
            {errors.taskType && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.taskType}
              </Typography>
            )}
          </FormControl>
        </Box>

        {/* Level 3: Framework */}
        <Box sx={{ position: "relative", ml: 4 }}>
          <FormControl fullWidth disabled={!taskType} error={!!errors.framework}>
            <FormLabel sx={{ mb: 1, fontWeight: "bold", color: "#2e7d32" }}>
              3. Model Framework <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              value={framework}
              onChange={handleFrameworkChange}
              displayEmpty
              sx={{
                backgroundColor: taskType ? "#f1f8e9" : "#f5f5f5",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#2e7d32",
                  borderWidth: 2,
                },
              }}
            >
              <MenuItem value="">
                <em>{taskType ? "Select Framework" : "Select Task Type first"}</em>
              </MenuItem>
              {getFrameworks().map((fw) => (
                <MenuItem key={fw} value={fw}>
                  {fw.charAt(0).toUpperCase() + fw.slice(1)}
                </MenuItem>
              ))}
            </Select>
            {errors.framework && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.framework}
              </Typography>
            )}
          </FormControl>
        </Box>

        {/* Level 4: Architecture */}
        <Box sx={{ position: "relative", ml: 6 }}>
          <FormControl fullWidth disabled={!framework} error={!!errors.architecture}>
            <FormLabel sx={{ mb: 1, fontWeight: "bold", color: "#ed6c02" }}>
              4. Model Architecture <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              value={architecture}
              onChange={handleArchitectureChange}
              displayEmpty
              sx={{
                backgroundColor: framework ? "#fff3e0" : "#f5f5f5",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#ed6c02",
                  borderWidth: 2,
                },
              }}
            >
              <MenuItem value="">
                <em>{framework ? "Select Architecture" : "Select Framework first"}</em>
              </MenuItem>
              {getArchitectures().map((arch) => (
                <MenuItem key={arch} value={arch}>
                  {arch.split("-").map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(" ")}
                </MenuItem>
              ))}
            </Select>
            {errors.architecture && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {errors.architecture}
              </Typography>
            )}
          </FormControl>
        </Box>
      </Box>
    </Box>
  );
};

export default HierarchicalModelSelector;
