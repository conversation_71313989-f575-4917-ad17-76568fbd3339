"use client";
import React, { useEffect, useState, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  Divider,
  Box,
  IconButton,
  Drawer,
  Input<PERSON>abel,
  SelectChangeEvent,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import "./style.css";
import FormFieldRenderer from "../../../../components/forms/renderFields";
import type {
  FormConfig,
  FormField,
  FormSection,
} from "../../../../components/interface/formInterface";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useRouter } from "next/navigation";
import Loader from "../../../../components/loader/loader";
import {
  desFormGet,
 
  registerCreate,
  sendforEvaluation,
} from "../../../../services/desApiService";
import AddIcon from "@mui/icons-material/Add";
import { toast } from "react-toastify";
import UploadDrawerForm from "../../datasets/create/uploadeDrawer";
 
import { docketUpload } from "../../../../services/uploadApiService";
import { taggingDatasetList } from "../../../../services/dataSetapiService";
import { tailwindStyles } from "../../../../styles/tailwindStyles";
 
 
export default function ProjectDocketForm() {
// ??for testing
const modelDependencyMap: Record<string, Record<string, string[]>> = {
  structured: {
    "tabular-classification": ["xgboost", "lightgbm", "scikit-learn", "tensorflow", "pytorch"],
    "tabular-regression": ["xgboost", "lightgbm", "scikit-learn", "tensorflow", "pytorch"]
  },
  x: {
    "image-classification": ["pytorch", "onnx", "tensorflow"],
    "image-segmentation": ["pytorch", "onnx", "tensorflow"]
  }
};
 
const frameworkToArchMap: Record<string, string[]> = {
  xgboost: ["tree-based", "neural-network", "custom", "linear-regression", "logistic-regression"],
  lightgbm: ["tree-based", "neural-network", "custom", "linear-regression", "logistic-regression"],
  "scikit-learn": ["tree-based", "neural-network", "custom", "linear-regression", "logistic-regression"],
  tensorflow: ["tree-based", "neural-network", "custom", "linear-regression", "logistic-regression"],
  pytorch: ["tree-based", "neural-network", "custom", "resnet-50", "efficientnet-b4", "vision-transformer", "linear-regression", "logistic-regression"],
  onnx: ["resnet-50", "efficientnet-b4", "vision-transformer", "custom", "linear-regression", "logistic-regression"]
};
 
  const router = useRouter();
  const [formConfig, setFormConfig] = useState<FormConfig>({
    sections: [],
    fields: [],
    type: 0,
    metadata: {
      dataType: "",
      taskType: "",
      modelFramework: "",
      modelArchitecture: "",
      modelWeightUrl: { path: "" },
      modelDatasetUrl: "",
    },
  });
  const [, setSubmitSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
 
  const [formStatus, setFormStatus] = useState<number | null>(null);
  type DatasetValue = {
    id: string;
    int_uuid: string;
    name: string;
  };
  const [datasetValues, setDatasetValues] = useState<DatasetValue[]>([]);
  console.log("datasetValues", datasetValues);
  const [uploadedFiles, setUploadedFiles] = useState<
    Record<string, { file_path: string; id: string }>
  >({});
  const [drawerContinued, setDrawerContinued] = useState(false);
  const [modelWeightUrl, setModelWeightUrl] = useState<{
    link: string;
    pat: string;
    type: string; // Add type to track the repository type
  }>({
    link: "",
    pat: "",
    type: "",
  });
 
  // for manually testing
  const [filteredTaskTypes, setFilteredTaskTypes] = useState<string[]>([]);
  const [filteredFrameworks, setFilteredFrameworks] = useState<string[]>([]);
  const [filteredArchitectures, setFilteredArchitectures] = useState<string[]>([]);
  const [renderKey, setRenderKey] = useState(0);
 
  const handleUploadedFile = (data: { file_path: string; id: string }) => {
    setUploadedFiles((prev) => ({
      ...prev,
      upload_file: data,
    }));
 
    const fileUploadField = formConfig.fields.find((field) => field.type === 7);
    if (fileUploadField) {
      formik.setFieldValue(fileUploadField.id.toString(), data);
    }
  };
 
  const handleLinkData = (data: { link: string; pat: string; type: string }) => {
    setModelWeightUrl(data); // Update the state with the repository type
    setDrawerContinued(true);
 
    // Also set the formik value if needed
    const fileUploadField = formConfig.fields.find((field) => field.type === 7);
    if (fileUploadField) {
      formik.setFieldValue(fileUploadField.id.toString(), {
        file_path: data.link,
        id: "link-upload",
        type: data.type, // Pass the type to formik
      });
    }
  };
 
  useEffect(() => {
    const fetchFormData = async () => {
      setLoading(true);
      try {
        const desformvalue = (await desFormGet({
          type: 3,
        })) as { data: FormConfig };
        setFormConfig(desformvalue.data);

        // Debug: Log all available options from API
        const architectureField = desformvalue.data.fields.find(f => f.label === "Model Architecture");
        const frameworkField = desformvalue.data.fields.find(f => f.label === "Model Framework");
        const taskTypeField = desformvalue.data.fields.find(f => f.label === "Task Type");
        const modelTypeField = desformvalue.data.fields.find(f => f.label === "Model Type");

        console.log("🔍 API Data - Available options:");
        console.log("  Model Types:", modelTypeField?.options?.map(opt => opt.value));
        console.log("  Task Types:", taskTypeField?.options?.map(opt => opt.value));
        console.log("  Frameworks:", frameworkField?.options?.map(opt => opt.value));
        console.log("  Architectures:", architectureField?.options?.map(opt => opt.value));

        const datasetFormvalue = await taggingDatasetList();
        setDatasetValues(datasetFormvalue?.data || []);
      } catch (err) {
        console.error("Failed to fetch form data", err);
      } finally {
        setLoading(false);
      }
    };
    fetchFormData();
  }, []);

  const handleOpenDrawer = () => {
    setOpen(true);
    setDrawerContinued(false);
  };
 
  const handleCloseDrawer = () => {
    setOpen(false);
  };
 
  const { initialValues, validationSchema } = useMemo(() => {
    const initialVals: Record<
      string,
      string | number | boolean | File | File[] | null
    > = {};
    const validationShape: Record<string, Yup.AnySchema> = {};
 
    for (const field of formConfig.fields) {
      initialVals[field.id] = "";
 
      if (field.required) {
        switch (field.type) {
          case 1:
            if (field.textfieldtype === "number") {
              let schema = Yup.number()
                .typeError("Must be a valid number")
                .required(`${field.label} is required`);
 
              if (field.min !== undefined) {
                schema = schema.min(field.min, `Minimum value is ${field.min}`);
              }
              if (field.max !== undefined) {
                schema = schema.max(field.max, `Maximum value is ${field.max}`);
              }
              validationShape[field.id] = schema;
            } else if (
              field.label.includes("email") ||
              field.label.includes("Email")
            ) {
              validationShape[field.id] = Yup.string()
                .matches(
                  /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                  "Must be a valid email address",
                )
                .required(`${field.label} is required`);
            } else if (
              field.label.includes("Phone Number") ||
              field.label.includes("phone number")
            ) {
              validationShape[field.id] = Yup.string()
                .matches(
                  /^[+]?[0-9]{1,4}?[-.\s\(\)]?(\(?[0-9]{1,3}?\)?[-.\s]?)?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,9}$/,
                  "Must be a valid phone number",
                )
                .required(`${field.label} is required`);
            } else {
              validationShape[field.id] = Yup.string().required(
                `${field.label} is required`,
              );
            }
            break;
 
          case 3:
            validationShape[field.id] = Yup.mixed().required(
              `${field.label} is required`,
            );
            break;
          case 7:
            validationShape[field.id] = Yup.mixed()
              .required("File upload is required")
              .test(
                "fileOrLink",
                "Either file upload or link is required",
                (value) => {
                  // Accept either file upload or link
                  return (
                    (typeof value === "object" && "file_path" in value) ||
                    modelWeightUrl.link !== ""
                  );
                }
              );
            break;
 
          default:
            validationShape[field.id] = Yup.string().required(
              `${field.label} is required`,
            );
        }
      }
    }
 
    return {
      initialValues: initialVals,
      validationSchema: Yup.object(validationShape),
    };
  }, [formConfig.fields, modelWeightUrl.link]);
 
  const getFieldValueByLabel = (label: string) => {
    const field = formConfig.fields.find((f) => f.label === label);
    if (label === "Tagging to sample datasets" && field) {
      // Return the selected dataset ID for "Tagging to sample datasets"
      return formik.values[field.id] || "";
    }
    return field ? formik.values[field.id] || "" : "";
  };
  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      try {
        const payload = {
          sections: formConfig.sections,
          fields: formConfig.fields.map((field) => {
            const value = values[field.id];
            if (
              field.type === 7 &&
              typeof value === "object" &&
              value !== null &&
              "file_path" in value
            ) {
              return { ...field, value };
            }
            return { ...field, value };
          }),
          type: formConfig.type,
          status: formStatus ?? 4,
          metadata: {
            ...formConfig.metadata,
            dataType: getFieldValueByLabel("Model Type"),
            taskType: getFieldValueByLabel("Task Type"),
            modelFramework: getFieldValueByLabel("Model Framework"),
            modelDatasetUrl: getFieldValueByLabel("Tagging to sample datasets"),
            modelArchitecture: getFieldValueByLabel("Model Architecture"),
            modelWeightUrl: modelWeightUrl.link
              ? { link: modelWeightUrl.link, pat: modelWeightUrl.pat, type: modelWeightUrl.type } // Include the type
              : { path: uploadedFiles.upload_file?.file_path || "" },
          },
        };
        // Call registerCreate API
        const response = await registerCreate(payload);
 
        // Extract the created docket UUID from the response
        const docketUuid = response?.data?.id;
 
        // Only call sendforEvaluation if formStatus is 4 (Send for Evaluation)
        if (formStatus === 4 && docketUuid) {
          await sendforEvaluation({ docket_uuid: docketUuid });
          toast.success("Project Dockets Created and Sent for Evaluation successfully");
        } else if (formStatus === 4) {
          toast.error("Failed to retrieve docket UUID");
        } else {
          toast.success("Project Dockets saved as draft successfully");
        }
 
        setSubmitSuccess(true);
        router.push("/project-dockets");
      } catch (error) {
        console.error("Registration failed:", error);
        toast.error("Failed to create and send for evaluation");
      }
    },
  });


useEffect(() => {
  console.log("✅ updated filteredTaskTypes:", filteredTaskTypes);
}, [filteredTaskTypes]);
 
useEffect(() => {
  console.log("✅ updated filteredFrameworks:", filteredFrameworks);
}, [filteredFrameworks]);
 
useEffect(() => {
  console.log("✅ updated filteredArchitectures:", filteredArchitectures);
}, [filteredArchitectures]);
 
  // test piece of code
const handleSelectChange = (
  e: SelectChangeEvent<string | string[]>,
  field: FormField
) => {
  const rawVal = e.target.value as string;
  const val = rawVal.toLowerCase().trim(); // Normalize for matching keys
  formik.setFieldValue(field.id.toString(), rawVal); // Keep original casing in form

  const getFieldId = (label: string) =>
    formConfig.fields.find((f) => f.label === label)?.id;

  console.log("==== BIDIRECTIONAL HANDLE SELECT CHANGE ====");
  console.log("Selected Field:", field.label);
  console.log("Selected Value (Raw):", rawVal);
  console.log("Normalized Value:", val);

  // Define hierarchical field relationships
  const hierarchicalFields = ["Model Type", "Task Type", "Model Framework", "Model Architecture"];

  if (hierarchicalFields.includes(field.label)) {
    // Get valid combinations for the selected field and value
    const validCombinations = getValidCombinations(field.label, val);
    console.log("Valid combinations for", field.label, "=", val, ":", validCombinations);

    // Get current form values for all fields
    const currentModelType = typeof getFieldValueByLabel("Model Type") === "string" ?
      (getFieldValueByLabel("Model Type") as string).toLowerCase().trim() : "";
    const currentTaskType = typeof getFieldValueByLabel("Task Type") === "string" ?
      (getFieldValueByLabel("Task Type") as string).toLowerCase().trim() : "";
    const currentFramework = typeof getFieldValueByLabel("Model Framework") === "string" ?
      (getFieldValueByLabel("Model Framework") as string).toLowerCase().trim() : "";
    const currentArchitecture = typeof getFieldValueByLabel("Model Architecture") === "string" ?
      (getFieldValueByLabel("Model Architecture") as string).toLowerCase().trim() : "";

    // Clear fields that are no longer valid based on the new selection
    if (field.label !== "Model Type" && currentModelType && !validCombinations.modelTypes.includes(currentModelType)) {
      console.log("→ Clearing Model Type (no longer valid)");
      formik.setFieldValue(getFieldId("Model Type")?.toString() || "", "");
    }

    if (field.label !== "Task Type" && currentTaskType && !validCombinations.taskTypes.includes(currentTaskType)) {
      console.log("→ Clearing Task Type (no longer valid)");
      formik.setFieldValue(getFieldId("Task Type")?.toString() || "", "");
    }

    if (field.label !== "Model Framework" && currentFramework && !validCombinations.frameworks.includes(currentFramework)) {
      console.log("→ Clearing Model Framework (no longer valid)");
      formik.setFieldValue(getFieldId("Model Framework")?.toString() || "", "");
    }

    if (field.label !== "Model Architecture" && currentArchitecture && !validCombinations.architectures.includes(currentArchitecture)) {
      console.log("→ Clearing Model Architecture (no longer valid)");
      formik.setFieldValue(getFieldId("Model Architecture")?.toString() || "", "");
    }

    // Update state arrays for debugging (optional)
    setFilteredTaskTypes(validCombinations.taskTypes);
    setFilteredFrameworks(validCombinations.frameworks);
    setFilteredArchitectures(validCombinations.architectures);

    // Force re-render for all hierarchical field changes
    setRenderKey(prev => prev + 1);
  }
};
 

  const handleChange = async (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | {
        target: {
          name: string;
          value: string | number | boolean | File | File[];
          type?: string;
          checked?: boolean;
          files?: FileList;
        };
      },
  ) => {
    const { name, value, type, checked, files } = e.target as HTMLInputElement;
 
    if (files && files.length > 0) {
      try {
        const formData = new FormData();
        formData.append("uploadFile", files[0]);
 
        const uploadRes = await docketUpload(formData);
        const fullPath = uploadRes?.data?.file_path || files[0].name;
        const fileId = uploadRes?.data?.id;
        const filePath = fullPath?.replace(/^Documents\//, "");
 
        const fileObj = { file_path: filePath, id: fileId };
 
        formik.setFieldValue(name, fileObj);
        setUploadedFiles((prev) => ({
          ...prev,
          [name]: fileObj,
        }));
        setDrawerContinued(true);
      } catch (error) {
        console.error("File upload failed:", error);
        formik.setFieldError(name, "File upload failed");
      }
    } else if (type === "checkbox" || type === "switch") {
      formik.setFieldValue(name, checked);
    } else {
      formik.setFieldValue(name, value);
    }
  };
 
  const handleFileDelete = async (fieldId: string) => {
    const fileData = uploadedFiles[fieldId];
    if (!fileData?.file_path) return;
 
    setUploadedFiles((prev) => {
      const updated = { ...prev };
      delete updated[fieldId];
      return updated;
    });
 
    const fileField = formConfig.fields.find(
      (field) => field.id.toString() === fieldId,
    );
    if (fileField) {
      formik.setFieldValue(fieldId, "");
    }
    setModelWeightUrl({ link: "", pat: "", type: "" }); // Reset modelWeightUrl
    setDrawerContinued(false);
  };
 
  const handleDataset = () => {
    router.push("/project-dockets");
  };
 
  // Add this function to check if the dataset field is empty
  const isDatasetFieldEmpty = () => {
    const datasetValue = getFieldValueByLabel("Tagging to sample datasets");
    return !datasetValue || datasetValue === "";
  };
 
// Helper function to check if architecture is compatible with task type
const isArchitectureCompatibleWithTaskType = (architecture: string, taskType: string): boolean => {
  // Define architecture categories - but allow more flexibility
  const strictlyTabularArchitectures = ["tree-based", "linear-regression", "logistic-regression"];
  const strictlyImageArchitectures = ["resnet-50", "vision-transformer"];
  const flexibleArchitectures = ["neural-network", "custom", "efficientnet-b4"]; // Can be used for both

  const isTabularTask = taskType.includes("tabular");
  const isImageTask = taskType.includes("image");

  // Strictly tabular architectures only work with tabular tasks
  if (strictlyTabularArchitectures.includes(architecture)) {
    return isTabularTask;
  }

  // Strictly image architectures only work with image tasks
  if (strictlyImageArchitectures.includes(architecture)) {
    return isImageTask;
  }

  // Flexible architectures work with both tabular and image tasks
  if (flexibleArchitectures.includes(architecture)) {
    return true;
  }

  return true; // Default to true for unknown architectures
};

// Helper function to get valid combinations based on any selected field
const getValidCombinations = (selectedField: string, selectedValue: string) => {
  console.log(`\n🔄 BIDIRECTIONAL FILTERING: ${selectedField} = "${selectedValue}"`);

  const validCombinations: {
    modelTypes: string[];
    taskTypes: string[];
    frameworks: string[];
    architectures: string[];
  } = {
    modelTypes: [],
    taskTypes: [],
    frameworks: [],
    architectures: []
  };

  if (selectedField === "Model Architecture") {
    console.log(`🔍 Looking for frameworks that support architecture: "${selectedValue}"`);
    console.log(`🔍 Available frameworkToArchMap:`, frameworkToArchMap);

    // Find all frameworks that support this architecture
    const supportingFrameworks = Object.keys(frameworkToArchMap).filter(fw => {
      const supports = frameworkToArchMap[fw].includes(selectedValue);
      console.log(`  - Framework "${fw}" supports "${selectedValue}": ${supports}`);
      return supports;
    });

    console.log(`🎯 Supporting frameworks for "${selectedValue}":`, supportingFrameworks);
    validCombinations.frameworks = supportingFrameworks;
    validCombinations.architectures = [selectedValue];

    // If no frameworks found, return empty combinations
    if (supportingFrameworks.length === 0) {
      console.log(`⚠️ No frameworks found for "${selectedValue}"`);
      return validCombinations;
    }

    // Find model types and task types that support these frameworks
    // BUT ALSO ensure the architecture makes sense for the task type
    for (const modelType of Object.keys(modelDependencyMap)) {
      for (const taskType of Object.keys(modelDependencyMap[modelType])) {
        const taskFrameworks = modelDependencyMap[modelType][taskType];

        // Check if any of the task's frameworks support this architecture
        const hasCompatibleFramework = taskFrameworks.some(fw =>
          supportingFrameworks.includes(fw)
        );

        if (hasCompatibleFramework) {
          // Additional logic: Check if architecture is appropriate for task type
          const isArchitectureAppropriate = isArchitectureCompatibleWithTaskType(selectedValue, taskType);

          if (isArchitectureAppropriate) {
            console.log(`  ✅ ${modelType} -> ${taskType} supports architecture "${selectedValue}"`);
            if (!validCombinations.modelTypes.includes(modelType)) {
              validCombinations.modelTypes.push(modelType);
            }
            if (!validCombinations.taskTypes.includes(taskType)) {
              validCombinations.taskTypes.push(taskType);
            }
          } else {
            console.log(`  ❌ ${modelType} -> ${taskType} has compatible frameworks but architecture "${selectedValue}" is not appropriate for this task type`);
          }
        } else {
          console.log(`  ❌ ${modelType} -> ${taskType} does not have frameworks that support "${selectedValue}"`);
        }
      }
    }
  }

  if (selectedField === "Model Framework") {
    console.log(`🔍 Looking for model types and task types that support framework: "${selectedValue}"`);
    console.log(`🔍 Checking dependency map:`, modelDependencyMap);

    validCombinations.frameworks = [selectedValue];
    validCombinations.architectures = frameworkToArchMap[selectedValue] || [];

    // Find model types and task types that support this framework
    for (const modelType of Object.keys(modelDependencyMap)) {
      console.log(`  🔍 Checking model type: "${modelType}"`);
      for (const taskType of Object.keys(modelDependencyMap[modelType])) {
        const taskFrameworks = modelDependencyMap[modelType][taskType];
        console.log(`    🔍 Task "${taskType}" frameworks:`, taskFrameworks);
        if (taskFrameworks.includes(selectedValue)) {
          console.log(`    ✅ Found: ${modelType} -> ${taskType} supports ${selectedValue}`);
          if (!validCombinations.modelTypes.includes(modelType)) {
            validCombinations.modelTypes.push(modelType);
          }
          if (!validCombinations.taskTypes.includes(taskType)) {
            validCombinations.taskTypes.push(taskType);
          }
        } else {
          console.log(`    ❌ ${modelType} -> ${taskType} does NOT support ${selectedValue}`);
        }
      }
    }

    console.log(`🎯 Valid combinations for framework "${selectedValue}":`, {
      modelTypes: validCombinations.modelTypes,
      taskTypes: validCombinations.taskTypes,
      frameworks: validCombinations.frameworks,
      architectures: validCombinations.architectures.slice(0, 5)
    });
  }

  if (selectedField === "Task Type") {
    console.log(`🔍 Looking for model types that support task type: "${selectedValue}"`);
    validCombinations.taskTypes = [selectedValue];

    // Find model types that support this task type
    for (const modelType of Object.keys(modelDependencyMap)) {
      if (modelDependencyMap[modelType][selectedValue]) {
        console.log(`  ✅ Found: Model Type "${modelType}" supports Task Type "${selectedValue}"`);
        console.log(`  📋 Frameworks for ${modelType}->${selectedValue}:`, modelDependencyMap[modelType][selectedValue]);
        validCombinations.modelTypes.push(modelType);
        validCombinations.frameworks.push(...modelDependencyMap[modelType][selectedValue]);
      } else {
        console.log(`  ❌ Model Type "${modelType}" does NOT support Task Type "${selectedValue}"`);
      }
    }

    // Remove duplicates from frameworks
    validCombinations.frameworks = [...new Set(validCombinations.frameworks)];
    console.log(`  🎯 Final frameworks for "${selectedValue}":`, validCombinations.frameworks);

    // Find architectures for these frameworks
    for (const framework of validCombinations.frameworks) {
      const archsForFramework = frameworkToArchMap[framework] || [];
      console.log(`  🏗️ Architectures for framework "${framework}":`, archsForFramework);
      validCombinations.architectures.push(...archsForFramework);
    }
    // Remove duplicates from architectures
    validCombinations.architectures = [...new Set(validCombinations.architectures)];

    console.log(`🎯 FINAL Valid combinations for task type "${selectedValue}":`, {
      modelTypes: validCombinations.modelTypes,
      frameworks: validCombinations.frameworks,
      architectures: validCombinations.architectures.length > 8 ?
        `${validCombinations.architectures.slice(0, 8).join(', ')}... (${validCombinations.architectures.length} total)` :
        validCombinations.architectures
    });
  }

  if (selectedField === "Model Type") {
    console.log(`📋 Model Type "${selectedValue}" selected - finding dependent options`);
    validCombinations.modelTypes = [selectedValue];
    validCombinations.taskTypes = Object.keys(modelDependencyMap[selectedValue] || {});
    console.log(`  ✅ Task Types: ${validCombinations.taskTypes.join(', ')}`);

    // Get all frameworks for this model type
    for (const taskType of validCombinations.taskTypes) {
      validCombinations.frameworks.push(...(modelDependencyMap[selectedValue][taskType] || []));
    }
    validCombinations.frameworks = [...new Set(validCombinations.frameworks)];
    console.log(`  ✅ Frameworks: ${validCombinations.frameworks.join(', ')}`);

    // Get all architectures for these frameworks
    for (const framework of validCombinations.frameworks) {
      validCombinations.architectures.push(...(frameworkToArchMap[framework] || []));
    }
    validCombinations.architectures = [...new Set(validCombinations.architectures)];
    console.log(`  ✅ Architectures: ${validCombinations.architectures.slice(0, 8).join(', ')}${validCombinations.architectures.length > 8 ? '...' : ''}`);
  }

  console.log(`🎯 FINAL RESULT for ${selectedField}="${selectedValue}":`, {
    modelTypes: validCombinations.modelTypes,
    taskTypes: validCombinations.taskTypes,
    frameworks: validCombinations.frameworks,
    architectures: validCombinations.architectures.length > 10 ?
      `${validCombinations.architectures.slice(0, 10).join(', ')}... (${validCombinations.architectures.length} total)` :
      validCombinations.architectures.join(', ')
  });

  return validCombinations;
};

const getOptions = (field: FormField) => {
  // Get current form values for all hierarchical fields
  const modelTypeValue = getFieldValueByLabel("Model Type");
  const taskTypeValue = getFieldValueByLabel("Task Type");
  const frameworkValue = getFieldValueByLabel("Model Framework");
  const architectureValue = getFieldValueByLabel("Model Architecture");

  const modelType = typeof modelTypeValue === "string" ? modelTypeValue.toLowerCase().trim() : "";
  const taskType = typeof taskTypeValue === "string" ? taskTypeValue.toLowerCase().trim() : "";
  const framework = typeof frameworkValue === "string" ? frameworkValue.toLowerCase().trim() : "";
  const architecture = typeof architectureValue === "string" ? architectureValue.toLowerCase().trim() : "";

  // Debug: Log all field options to see what the API returns
  const hierarchicalFields = ["Model Type", "Task Type", "Model Framework", "Model Architecture"];
  if (hierarchicalFields.includes(field.label)) {
    console.log(`🔍 ${field.label} - Current values:`, { modelType, taskType, framework, architecture });
  }

  // Determine which field has a value to base filtering on
  // Special logic: For specific dropdowns, prioritize the most relevant field
  let filteringField = "";
  let filteringValue = "";

  if (field.label === "Model Type" && taskType && !modelType) {
    // When filtering Model Type dropdown, use Task Type as primary filter ONLY if no Model Type is selected yet
    filteringField = "Task Type";
    filteringValue = taskType;
    console.log(`🎯 Model Type dropdown: Using Task Type "${taskType}" as primary filter`);
  }
  else if (field.label === "Task Type" && modelType) {
    // When filtering Task Type dropdown, use Model Type as primary filter if available
    filteringField = "Model Type";
    filteringValue = modelType;
    console.log(`🎯 Task Type dropdown: Using Model Type "${modelType}" as primary filter`);
  }
  else if (field.label === "Task Type" && framework) {
    // When filtering Task Type dropdown, use Framework as secondary filter if no Model Type
    filteringField = "Model Framework";
    filteringValue = framework;
    console.log(`🎯 Task Type dropdown: Using Framework "${framework}" as primary filter`);
  }
  else if (field.label === "Model Framework" && modelType) {
    // When filtering Model Framework dropdown, use Model Type as primary filter if available
    filteringField = "Model Type";
    filteringValue = modelType;
    console.log(`🎯 Model Framework dropdown: Using Model Type "${modelType}" as primary filter`);
  }
  else if (field.label === "Model Framework" && taskType) {
    // When filtering Model Framework dropdown, use Task Type as secondary filter if no Model Type
    filteringField = "Task Type";
    filteringValue = taskType;
    console.log(`🎯 Model Framework dropdown: Using Task Type "${taskType}" as primary filter`);
  }
  else if (field.label === "Model Architecture" && modelType) {
    // When filtering Model Architecture dropdown, use Model Type as primary filter if available
    filteringField = "Model Type";
    filteringValue = modelType;
    console.log(`🎯 Model Architecture dropdown: Using Model Type "${modelType}" as primary filter`);
  }
  else if (field.label === "Model Architecture" && taskType) {
    // When filtering Model Architecture dropdown, use Task Type as secondary filter if no Model Type
    filteringField = "Task Type";
    filteringValue = taskType;
    console.log(`🎯 Model Architecture dropdown: Using Task Type "${taskType}" as primary filter`);
  }
  else if (field.label === "Model Architecture" && framework) {
    // When filtering Model Architecture dropdown, use Framework as tertiary filter
    filteringField = "Model Framework";
    filteringValue = framework;
    console.log(`🎯 Model Architecture dropdown: Using Framework "${framework}" as primary filter`);
  }
  else if (architecture) {
    filteringField = "Model Architecture";
    filteringValue = architecture;
    console.log(`🎯 Using Architecture "${architecture}" as primary filter`);
  }
  else if (framework) {
    filteringField = "Model Framework";
    filteringValue = framework;
    console.log(`🎯 Using Framework "${framework}" as primary filter`);
  }
  else if (taskType) {
    filteringField = "Task Type";
    filteringValue = taskType;
    console.log(`🎯 Using Task Type "${taskType}" as primary filter`);
  }
  else if (modelType) {
    filteringField = "Model Type";
    filteringValue = modelType;
    console.log(`🎯 Using Model Type "${modelType}" as primary filter`);
  }

  if (!filteringField) {
    // No hierarchical field selected, show all options
    console.log(`🔍 No filtering field found, showing all options for ${field.label}`);
    return field.options || [];
  }

  // Special case: If user is trying to change Model Type and no other constraining fields are selected,
  // show all Model Type options to allow selection change
  if (field.label === "Model Type" && modelType && !taskType && !framework && !architecture) {
    console.log(`🔍 Model Type already selected but no other constraints, showing all Model Type options`);
    return field.options || [];
  }

  // Get valid combinations based on the selected field
  const validCombinations = getValidCombinations(filteringField, filteringValue);
  console.log(`🔍 Filtering based on ${filteringField}="${filteringValue}":`, validCombinations);

  // Filter options based on valid combinations
  switch (field.label) {
    case "Model Type":
      console.log(`🔍 Model Type filtering:`, {
        allOptions: field.options?.map(opt => opt.value),
        validModelTypes: validCombinations.modelTypes,
        filteredOptions: field.options?.filter(opt =>
          validCombinations.modelTypes.includes(opt.value.toLowerCase().trim())
        ).map(opt => opt.value)
      });
      return field.options?.filter(opt =>
        validCombinations.modelTypes.includes(opt.value.toLowerCase().trim())
      ) || [];

    case "Task Type":
      console.log(`🔍 Task Type filtering:`, {
        allOptions: field.options?.map(opt => opt.value),
        validTaskTypes: validCombinations.taskTypes,
        filteredOptions: field.options?.filter(opt =>
          validCombinations.taskTypes.includes(opt.value.toLowerCase().trim())
        ).map(opt => opt.value)
      });
      return field.options?.filter(opt =>
        validCombinations.taskTypes.includes(opt.value.toLowerCase().trim())
      ) || [];

    case "Model Framework":
      console.log(`🔍 Model Framework filtering:`, {
        allOptions: field.options?.map(opt => opt.value),
        validFrameworks: validCombinations.frameworks,
        filteredOptions: field.options?.filter(opt =>
          validCombinations.frameworks.includes(opt.value.toLowerCase().trim())
        ).map(opt => opt.value)
      });
      return field.options?.filter(opt =>
        validCombinations.frameworks.includes(opt.value.toLowerCase().trim())
      ) || [];

    case "Model Architecture":
      return field.options?.filter(opt =>
        validCombinations.architectures.includes(opt.value.toLowerCase().trim())
      ) || [];

    default:
      return field.options || [];
  }
};
 
  return (
    <>
      {loading && <Loader />}
      <div className="min-h-screen flex items-center justify-center p-4 relative d-flex">
        <div className="w-full max-w-5xl">
          <Box className="flex justify-between items-center mb-6 p-4 rounded-t-lg z-50 sticky top-0 sticky backdrop-blur-sm">
            <Typography variant="h5" className="font-bold text-[#000000]">
              Create Project Dockets
            </Typography>
            <Button
              style={{ background: "white", color: "black" }}
              variant="contained"
              startIcon={<ArrowBackIcon />}
              onClick={handleDataset}
            >
              Back
            </Button>
          </Box>
 
          <Card className="w-full max-w-5xl backdrop-blur-sm bg-white/90 overflow-auto shadow-xl hover:shadow-4xl transition-shadow duration-300">
            <form onSubmit={formik.handleSubmit} noValidate>
              {formConfig.sections
                .sort((a, b) => (a.position || 0) - (b.position || 0))
                .map((section: FormSection) => (
                  <div key={section.id} className="mb-4">
                    <Typography variant="h6" className="mb-4 p-4">
                      {section.label}
                    </Typography>
                    <Divider className="mb-4" />
                    <div className="grid pt-4 pl-8 pr-8 grid-cols-1 md:grid-cols-2 gap-4">
                      {formConfig.fields
                        .filter(
                          (field: FormField) => field.section_id === section.id
                        )
                        .sort((a, b) => (a.position || 0) - (b.position || 0))
                        .map((field: FormField) => (
                          <div key={field.id}>
                            {field.type !== 7 ? (
                              <FormFieldRenderer
                                key={`${field.id}-${renderKey}`}
                                field={{
                                  ...field,
                                  value: formik.values[field.id] as
                                    | string
                                    | number
                                    | boolean
                                    | File
                                    | File[]
                                    | null,
                                }}
                                error={
                                  formik.touched[field.id] &&
                                    formik.errors[field.id]
                                    ? formik.errors[field.id]
                                    : ""
                                }
                                onChange={handleChange}
                                onSelectChange={(e) =>
                                  handleSelectChange(
                                    e as SelectChangeEvent<string | string[]>,
                                    field,
                                  )
                                }
                                options={
                                  field.type === 9 &&
                                    field.label === "Tagging to sample datasets"
                                    ? datasetValues.map((ds) => ({
                                      id: ds.int_uuid,
                                      name: ds.name,
                                      value:
                                        ds.name ||
                                        "Unnamed Dataset",
                                    }))
                                    : getOptions(field)
                                }

                              />
                            ) : (
                              <Box>
                                {uploadedFiles.upload_file && drawerContinued ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "space-between",
                                      border: "1px solid #ddd",
                                      borderRadius: "4px",
                                      padding: "8px 16px",
                                      backgroundColor: "#fff3eb"
                                    }}
                                  >
                                    <Typography variant="body1">
                                      {uploadedFiles.upload_file.file_path
                                        .length > 30
                                        ? `${uploadedFiles.upload_file.file_path.slice(0, 30)}...`
                                        : uploadedFiles.upload_file.file_path}
                                    </Typography>
                                    <Button
                                      onClick={() =>
                                        handleFileDelete("upload_file")
                                      }
                                      color="error"
                                      size="small"
                                    >
                                      Remove
                                    </Button>
                                  </Box>
                                ) : modelWeightUrl.link ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "space-between",
                                      border: "1px solid #ddd",
                                      borderRadius: "4px",
                                      padding: "8px 16px",
                                      backgroundColor: "#fff3eb",
                                    }}
 
 
                                  >
                                    <Typography variant="body1">
                                      {modelWeightUrl.link.length > 30
                                        ? `${modelWeightUrl.link.slice(0, 30)}...`
                                        : modelWeightUrl.link}
                                    </Typography>
                                    <Button
                                      onClick={() => {
                                        setModelWeightUrl({ link: "", pat: "", type: "" });
                                        setDrawerContinued(false);
                                      }}
                                      color="error"
                                      size="small"
                                    >
                                      Remove
                                    </Button>
                                  </Box>
                                ) : (
                                  <>
                                    <InputLabel sx={{ paddingBottom: "7px" }}>Upload Project Docket <span className="text-red-500 ml-1">*</span></InputLabel>
                                    <Box
                                      className="upload-button uploaded-button inputBackground "
                                      onClick={handleOpenDrawer}
                                    >
                                      <Typography
                                        variant="body1"
                                        sx={{ color: "text.secondary" }}
                                      >
                                        Upload Project Docket
                                      </Typography>
                                      <IconButton size="small" color="default">
                                        <AddIcon />
                                      </IconButton>
                                    </Box>
                                  </>
                                )}
                                {formik.touched[field.id] &&
                                  formik.errors[field.id] && (
                                    <Typography
                                      color="error"
                                      variant="body2"
                                      sx={{ mt: 1 }}
                                    >
                                      {formik.errors[field.id] as string}
                                    </Typography>
                                  )}
                              </Box>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              <Box className="flex flex-col sm:flex-row items-center justify-center gap-4 p-5">
                <button
                  type="button"
                  onClick={() => {
                    setFormStatus(3); // Save as Draft
                    formik.handleSubmit();
                  }}
                  style={{
                    padding: "9px",
                    width: "200px",
                    fontSize: "15px",
                  }}
                  className={tailwindStyles.saveDraft}
                >
                  Save as Draft
                </button>
 
                <Button
                  type="submit"
                  onClick={() => setFormStatus(4)} // Send for Evaluation
                  variant="contained"
                  disabled={isDatasetFieldEmpty()}
                  style={{
                    padding: "9px",
                    width: "200px",
                    fontSize: "15px",
                    color: "white",
                  }}
                  className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r font-bold from-[#F45C24] to-[#FFCB80]  text-sm sm:text-base text-transform-none"
                >
                  Send for Evaluation
                </Button>
              </Box>
            </form>
          </Card>
        </div>
      </div>
 
      <Drawer
        anchor="right"
        open={open}
        onClose={handleCloseDrawer}
        slotProps={{
          paper: {
            sx: {
              width: "700px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
            },
          },
        }}
      >
        <UploadDrawerForm
          setOpen={setOpen}
          onFileUpload={handleUploadedFile}
          onContinue={handleLinkData} // Pass the updated handler
        />
      </Drawer>
     
    </>
  );
}